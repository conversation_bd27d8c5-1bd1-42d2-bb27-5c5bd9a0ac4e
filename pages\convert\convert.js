const app = getApp();

Page({
  data: {
    type: 'length', // 当前换算类型
    categoryInfo: {}, // 分类信息
    unitList: [], // 单位列表
    commonUnits: [], // 常用单位
    fromUnit: {}, // 输入单位
    toUnit: {}, // 输出单位
    inputValue: '', // 输入值
    resultValue: '', // 结果值
    inputFocus: false, // 输入框焦点
    showPicker: false, // 显示选择器
    pickerType: 'from', // 选择器类型
    historyList: [], // 历史记录
    showResult: false, // 是否显示结果模式
    resultMode: false // 结果模式标识
  },

  onLoad(options) {
    console.log('换算页面加载', options);
    const type = options.type || 'length';
    const showResult = options.showResult === 'true';

    this.setData({
      type: type,
      showResult: showResult,
      resultMode: showResult
    });

    this.initPage();
    this.loadHistory();

    // 如果是结果模式，恢复换算数据
    if (showResult) {
      this.restoreConvertData();
    }
  },

  onShow() {
    console.log('换算页面显示');
    this.setData({
      inputFocus: true
    });
  },

  // 初始化页面
  initPage() {
    const unitData = app.globalData.unitData[this.data.type];
    if (!unitData) {
      tt.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
      return;
    }

    const unitList = [];
    const commonUnits = [];
    
    for (let key in unitData.units) {
      const unit = {
        key: key,
        name: unitData.units[key].name,
        ratio: unitData.units[key].ratio,
        convert: unitData.units[key].convert
      };
      unitList.push(unit);
      
      // 添加常用单位（前5个）
      if (commonUnits.length < 5) {
        commonUnits.push(unit);
      }
    }

    this.setData({
      categoryInfo: {
        name: unitData.name,
        desc: this.getCategoryDesc(this.data.type)
      },
      unitList: unitList,
      commonUnits: commonUnits,
      fromUnit: unitList[0] || {},
      toUnit: unitList[1] || unitList[0] || {}
    });
  },

  // 获取分类描述
  getCategoryDesc(type) {
    const descriptions = {
      length: '支持米、厘米、英寸、英尺等长度单位换算',
      weight: '支持千克、克、磅、盎司等重量单位换算',
      volume: '支持升、毫升、加仑等体积单位换算',
      temperature: '支持摄氏度、华氏度、开尔文等温度换算'
    };
    return descriptions[type] || '';
  },

  // 输入值变化
  onInputChange(e) {
    const value = e.detail.value;
    this.setData({
      inputValue: value
    });
    this.calculateResult(value);
  },

  // 计算结果
  calculateResult(inputValue) {
    if (!inputValue || isNaN(inputValue)) {
      this.setData({
        resultValue: ''
      });
      return;
    }

    const input = parseFloat(inputValue);
    let result = 0;

    if (this.data.type === 'temperature') {
      // 温度换算特殊处理
      result = this.convertTemperature(input, this.data.fromUnit.key, this.data.toUnit.key);
    } else {
      // 其他单位换算
      const fromRatio = this.data.fromUnit.ratio || 1;
      const toRatio = this.data.toUnit.ratio || 1;
      result = (input * fromRatio) / toRatio;
    }

    // 格式化结果
    const formattedResult = this.formatNumber(result);
    this.setData({
      resultValue: formattedResult
    });

    // 保存到历史记录
    this.saveToHistory(inputValue, this.data.fromUnit.name, formattedResult, this.data.toUnit.name);
  },

  // 温度换算
  convertTemperature(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;

    let celsius = value;
    
    // 转换为摄氏度
    switch (fromUnit) {
      case 'f': // 华氏度转摄氏度
        celsius = (value - 32) * 5 / 9;
        break;
      case 'k': // 开尔文转摄氏度
        celsius = value - 273.15;
        break;
    }

    // 从摄氏度转换为目标单位
    switch (toUnit) {
      case 'c':
        return celsius;
      case 'f': // 摄氏度转华氏度
        return celsius * 9 / 5 + 32;
      case 'k': // 摄氏度转开尔文
        return celsius + 273.15;
      default:
        return celsius;
    }
  },

  // 格式化数字
  formatNumber(num) {
    if (Math.abs(num) < 0.0001) {
      return num.toExponential(4);
    } else if (Math.abs(num) >= 1000000) {
      return num.toExponential(4);
    } else {
      return parseFloat(num.toFixed(8)).toString();
    }
  },

  // 显示单位选择器
  showFromUnitPicker() {
    this.setData({
      showPicker: true,
      pickerType: 'from'
    });
  },

  showToUnitPicker() {
    this.setData({
      showPicker: true,
      pickerType: 'to'
    });
  },

  // 隐藏选择器
  hidePicker() {
    this.setData({
      showPicker: false
    });
  },

  // 选择单位
  selectUnit(e) {
    const unitKey = e.currentTarget.dataset.unit;
    const unit = this.data.unitList.find(u => u.key === unitKey);
    
    if (this.data.pickerType === 'from') {
      this.setData({
        fromUnit: unit,
        showPicker: false
      });
    } else {
      this.setData({
        toUnit: unit,
        showPicker: false
      });
    }

    // 重新计算结果
    if (this.data.inputValue) {
      this.calculateResult(this.data.inputValue);
    }
  },

  // 快速选择单位
  selectQuickUnit(e) {
    const unitKey = e.currentTarget.dataset.unit;
    const type = e.currentTarget.dataset.type;
    const unit = this.data.unitList.find(u => u.key === unitKey);
    
    if (type === 'from') {
      this.setData({
        fromUnit: unit
      });
    } else {
      this.setData({
        toUnit: unit
      });
    }

    // 重新计算结果
    if (this.data.inputValue) {
      this.calculateResult(this.data.inputValue);
    }
  },

  // 交换单位
  swapUnits() {
    const fromUnit = this.data.fromUnit;
    const toUnit = this.data.toUnit;
    const inputValue = this.data.inputValue;
    const resultValue = this.data.resultValue;

    this.setData({
      fromUnit: toUnit,
      toUnit: fromUnit,
      inputValue: resultValue,
      resultValue: inputValue
    });

    tt.vibrateShort();
  },

  // 清除输入
  clearInput() {
    this.setData({
      inputValue: '',
      resultValue: '',
      inputFocus: true
    });
  },

  // 复制结果
  copyResult() {
    if (!this.data.resultValue) return;

    tt.setClipboardData({
      data: this.data.resultValue,
      success: () => {
        tt.showToast({
          title: '已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  // 执行换算（跳转到广告页面）
  performConvert() {
    if (!this.data.inputValue || !this.data.resultValue) {
      tt.showToast({
        title: '请先输入数值',
        icon: 'none'
      });
      return;
    }

    // 保存换算数据到全局
    app.globalData.convertData = {
      type: this.data.type,
      inputValue: this.data.inputValue,
      fromUnit: this.data.fromUnit,
      toUnit: this.data.toUnit,
      resultValue: this.data.resultValue
    };

    // 跳转到广告页面
    tt.navigateTo({
      url: `/pages/ad/ad?type=${this.data.type}&from=convert`,
      success: () => {
        console.log('跳转到广告页面');
      },
      fail: (err) => {
        console.error('跳转失败', err);
        tt.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 恢复换算数据（结果模式）
  restoreConvertData() {
    const convertData = app.globalData.convertData;
    if (!convertData) {
      console.error('没有换算数据');
      return;
    }

    // 恢复数据
    this.setData({
      inputValue: convertData.inputValue,
      fromUnit: convertData.fromUnit,
      toUnit: convertData.toUnit,
      resultValue: convertData.resultValue,
      inputFocus: false
    });

    // 保存到历史记录
    this.saveToHistory(
      convertData.inputValue,
      convertData.fromUnit.name,
      convertData.resultValue,
      convertData.toUnit.name
    );

    // 显示成功提示
    tt.showToast({
      title: '换算完成',
      icon: 'success'
    });

    // 清除全局数据
    app.globalData.convertData = null;
  },

  // 返回换算模式
  backToConvert() {
    this.setData({
      resultMode: false,
      showResult: false,
      inputFocus: true
    });
  },

  // 保存到历史记录
  saveToHistory(input, fromUnit, result, toUnit) {
    if (!input || !result) return;

    const historyItem = {
      input: input,
      fromUnit: fromUnit,
      result: result,
      toUnit: toUnit,
      time: this.formatTime(new Date()),
      type: this.data.type
    };

    let historyList = this.data.historyList;
    
    // 检查是否已存在相同记录
    const exists = historyList.some(item => 
      item.input === input && 
      item.fromUnit === fromUnit && 
      item.toUnit === toUnit
    );

    if (!exists) {
      historyList.unshift(historyItem);
      // 限制历史记录数量
      if (historyList.length > 10) {
        historyList = historyList.slice(0, 10);
      }

      this.setData({
        historyList: historyList
      });

      // 保存到本地存储
      tt.setStorageSync(`history_${this.data.type}`, historyList);
    }
  },

  // 加载历史记录
  loadHistory() {
    try {
      const historyList = tt.getStorageSync(`history_${this.data.type}`) || [];
      this.setData({
        historyList: historyList
      });
    } catch (e) {
      console.error('加载历史记录失败', e);
    }
  },

  // 使用历史记录
  useHistoryItem(e) {
    const item = e.currentTarget.dataset.item;
    const fromUnit = this.data.unitList.find(u => u.name === item.fromUnit);
    const toUnit = this.data.unitList.find(u => u.name === item.toUnit);

    if (fromUnit && toUnit) {
      this.setData({
        fromUnit: fromUnit,
        toUnit: toUnit,
        inputValue: item.input,
        resultValue: item.result,
        inputFocus: true
      });
    }
  },

  // 清空历史记录
  clearHistory() {
    tt.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            historyList: []
          });
          tt.removeStorageSync(`history_${this.data.type}`);
          tt.showToast({
            title: '已清空历史记录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 格式化时间
  formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `${this.data.categoryInfo.name}换算工具`,
      path: `/pages/convert/convert?type=${this.data.type}`,
      imageUrl: '/images/share.png'
    };
  }
});