/**
 * 单位换算工具类
 */
class UnitConverter {
  constructor() {
    // 长度换算基准：米
    this.lengthUnits = {
      'm': 1,
      'cm': 0.01,
      'mm': 0.001,
      'km': 1000,
      'in': 0.0254,
      'ft': 0.3048,
      'yd': 0.9144,
      'mi': 1609.344
    };

    // 重量换算基准：千克
    this.weightUnits = {
      'kg': 1,
      'g': 0.001,
      't': 1000,
      'lb': 0.453592,
      'oz': 0.0283495,
      'st': 6.35029
    };

    // 体积换算基准：升
    this.volumeUnits = {
      'l': 1,
      'ml': 0.001,
      'm3': 1000,
      'cm3': 0.001,
      'gal': 3.78541,
      'qt': 0.946353,
      'pt': 0.473176,
      'cup': 0.236588,
      'fl_oz': 0.0295735
    };
  }

  /**
   * 通用单位换算
   * @param {number} value - 输入值
   * @param {string} fromUnit - 源单位
   * @param {string} toUnit - 目标单位
   * @param {string} type - 换算类型
   * @returns {number} 换算结果
   */
  convert(value, fromUnit, toUnit, type) {
    if (fromUnit === toUnit) return value;

    switch (type) {
      case 'length':
        return this.convertLength(value, fromUnit, toUnit);
      case 'weight':
        return this.convertWeight(value, fromUnit, toUnit);
      case 'volume':
        return this.convertVolume(value, fromUnit, toUnit);
      case 'temperature':
        return this.convertTemperature(value, fromUnit, toUnit);
      default:
        return value;
    }
  }

  /**
   * 长度换算
   */
  convertLength(value, fromUnit, toUnit) {
    const fromRatio = this.lengthUnits[fromUnit] || 1;
    const toRatio = this.lengthUnits[toUnit] || 1;
    return (value * fromRatio) / toRatio;
  }

  /**
   * 重量换算
   */
  convertWeight(value, fromUnit, toUnit) {
    const fromRatio = this.weightUnits[fromUnit] || 1;
    const toRatio = this.weightUnits[toUnit] || 1;
    return (value * fromRatio) / toRatio;
  }

  /**
   * 体积换算
   */
  convertVolume(value, fromUnit, toUnit) {
    const fromRatio = this.volumeUnits[fromUnit] || 1;
    const toRatio = this.volumeUnits[toUnit] || 1;
    return (value * fromRatio) / toRatio;
  }

  /**
   * 温度换算
   */
  convertTemperature(value, fromUnit, toUnit) {
    if (fromUnit === toUnit) return value;

    let celsius = value;
    
    // 转换为摄氏度
    switch (fromUnit) {
      case 'f': // 华氏度转摄氏度
        celsius = (value - 32) * 5 / 9;
        break;
      case 'k': // 开尔文转摄氏度
        celsius = value - 273.15;
        break;
      case 'r': // 兰氏度转摄氏度
        celsius = (value - 491.67) * 5 / 9;
        break;
    }

    // 从摄氏度转换为目标单位
    switch (toUnit) {
      case 'c':
        return celsius;
      case 'f': // 摄氏度转华氏度
        return celsius * 9 / 5 + 32;
      case 'k': // 摄氏度转开尔文
        return celsius + 273.15;
      case 'r': // 摄氏度转兰氏度
        return (celsius + 273.15) * 9 / 5;
      default:
        return celsius;
    }
  }

  /**
   * 格式化数字显示
   */
  formatNumber(num, precision = 8) {
    if (isNaN(num) || !isFinite(num)) return '0';
    
    const absNum = Math.abs(num);
    
    if (absNum < 0.0001 && absNum > 0) {
      return num.toExponential(4);
    } else if (absNum >= 1000000) {
      return num.toExponential(4);
    } else {
      return parseFloat(num.toFixed(precision)).toString();
    }
  }

  /**
   * 验证输入值
   */
  validateInput(value) {
    if (value === '' || value === null || value === undefined) {
      return { valid: false, message: '请输入数值' };
    }
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return { valid: false, message: '请输入有效数字' };
    }
    
    if (!isFinite(num)) {
      return { valid: false, message: '数值超出范围' };
    }
    
    return { valid: true, value: num };
  }

  /**
   * 获取单位信息
   */
  getUnitInfo(unit, type) {
    const unitMaps = {
      length: this.lengthUnits,
      weight: this.weightUnits,
      volume: this.volumeUnits
    };
    
    const unitMap = unitMaps[type];
    if (unitMap && unitMap[unit]) {
      return {
        key: unit,
        ratio: unitMap[unit]
      };
    }
    
    return null;
  }
}

// 导出单例
const converter = new UnitConverter();

module.exports = converter;