.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 30rpx;
}

/* 顶部标题区域 */
.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.category-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 10rpx;
}

.category-desc {
  display: block;
  font-size: 26rpx;
  color: #666666;
}

/* 输入区域 */
.input-section {
  margin-bottom: 40rpx;
}

.input-card {
  background: #ffffff;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.input-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.input-label {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.clear-btn {
  background: transparent;
  color: #FE2C55;
  border: 1rpx solid #FE2C55;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  line-height: 1;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.number-input {
  flex: 1;
  background: #f8f9fa;
  border: 1rpx solid rgba(0, 0, 0, 0.12);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  font-size: 36rpx;
  color: #1a1a1a;
  text-align: center;
}

.number-input::placeholder {
  color: #999999;
}

.unit-selector {
  background: rgba(254, 44, 85, 0.05);
  border: 1rpx solid rgba(254, 44, 85, 0.2);
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  min-width: 140rpx;
  justify-content: center;
}

.unit-text {
  font-size: 28rpx;
  color: #FE2C55;
  font-weight: bold;
}

.unit-arrow {
  font-size: 20rpx;
  color: #FE2C55;
}

/* 转换箭头 */
.convert-arrow {
  text-align: center;
  margin: 30rpx 0;
}

.arrow-icon {
  font-size: 48rpx;
  color: #FE2C55;
  margin-bottom: 10rpx;
  display: block;
  transform: rotate(90deg);
}

.arrow-text {
  font-size: 24rpx;
  color: #666666;
}

/* 结果区域 */
.result-section {
  margin-bottom: 40rpx;
}

.result-card {
  background: #ffffff;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.result-label {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.copy-btn {
  background: transparent;
  color: #FE2C55;
  border: 1rpx solid #FE2C55;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  line-height: 1;
}

.result-group {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.result-value {
  flex: 1;
  font-size: 36rpx;
  color: #FE2C55;
  font-weight: bold;
  text-align: center;
  padding: 24rpx 20rpx;
  background: rgba(254, 44, 85, 0.05);
  border: 1rpx solid rgba(254, 44, 85, 0.15);
  border-radius: 16rpx;
}

/* 常用单位 */
.quick-units {
  margin-bottom: 40rpx;
}

.quick-title {
  display: block;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.quick-scroll {
  white-space: nowrap;
}

.quick-unit-list {
  display: flex;
  gap: 15rpx;
}

.quick-unit {
  background: #ffffff;
  border: 1rpx solid rgba(0, 0, 0, 0.12);
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  color: #1a1a1a;
  white-space: nowrap;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.quick-unit.active {
  background: rgba(254, 44, 85, 0.1);
  border-color: #FE2C55;
  color: #FE2C55;
  box-shadow: 0 4rpx 12rpx rgba(254, 44, 85, 0.15);
}

/* 历史记录 */
.history-section {
  margin-bottom: 40rpx;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.clear-history-btn {
  background: transparent;
  color: #666666;
  border: 1rpx solid rgba(0, 0, 0, 0.12);
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  line-height: 1;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  background: #ffffff;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.history-text {
  display: block;
  font-size: 26rpx;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.history-time {
  font-size: 22rpx;
  color: #999999;
}

/* 单位选择器弹窗 */
.picker-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.picker-container {
  width: 100%;
  max-height: 70vh;
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.08);
}

.picker-title {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.picker-close {
  background: transparent;
  color: #666666;
  border: none;
  font-size: 40rpx;
  line-height: 1;
  padding: 0;
  width: 60rpx;
  height: 60rpx;
}

.picker-list {
  max-height: 50vh;
}

.picker-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.picker-item.selected {
  background: rgba(254, 44, 85, 0.05);
}

.picker-unit-name {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.picker-unit-key {
  font-size: 24rpx;
  color: #666666;
}

.picker-check {
  font-size: 32rpx;
  color: #FE2C55;
  font-weight: bold;
}

/* 动画效果 */
.convert-arrow {
  transition: transform 0.3s ease;
}

.convert-arrow:active {
  transform: scale(1.1);
}

/* 响应式适配 */
@media (max-width: 768rpx) {
  .container {
    padding: 25rpx;
  }
  
  .category-title {
    font-size: 44rpx;
  }
  
  .category-desc {
    font-size: 24rpx;
  }
  
  .input-card, .result-card {
    padding: 25rpx;
  }
  
  .quick-units {
    margin-bottom: 35rpx;
  }
}

@media (max-width: 480rpx) {
  .container {
    padding: 20rpx;
  }
  
  .header {
    margin-bottom: 50rpx;
  }
  
  .category-title {
    font-size: 40rpx;
  }
  
  .input-group, .result-group {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .unit-selector {
    width: 100%;
    min-width: auto;
  }
  
  .number-input, .result-value {
    font-size: 32rpx;
    text-align: left;
  }
  
  .convert-arrow {
    margin: 25rpx 0;
  }
  
  .arrow-icon {
    font-size: 40rpx;
  }
  
  .quick-scroll {
    margin: 0 -20rpx;
    padding: 0 20rpx;
  }
  
  .history-section {
    margin-bottom: 30rpx;
  }
}

@media (max-width: 375px) {
  .container {
    padding: 15rpx;
  }
  
  .category-title {
    font-size: 36rpx;
  }
  
  .category-desc {
    font-size: 22rpx;
  }
  
  .input-card, .result-card {
    padding: 20rpx;
  }
  
  .number-input, .result-value {
    font-size: 30rpx;
    padding: 20rpx 15rpx;
  }
  
  .unit-text {
    font-size: 26rpx;
  }
  
  .quick-unit {
    font-size: 24rpx;
    padding: 14rpx 20rpx;
  }
  
  .history-item {
    padding: 15rpx;
  }
  
  .history-text {
    font-size: 24rpx;
  }
  
  .picker-container {
    max-height: 75vh;
  }
  
  .picker-item {
    padding: 25rpx;
  }
  
  .picker-unit-name {
    font-size: 28rpx;
  }
}

@media (max-width: 320px) {
  .container {
    padding: 10rpx;
  }
  
  .category-title {
    font-size: 32rpx;
  }
  
  .input-card, .result-card {
    padding: 15rpx;
  }
  
  .number-input, .result-value {
    font-size: 28rpx;
    padding: 18rpx 12rpx;
  }
  
  .unit-selector {
    padding: 18rpx 15rpx;
  }
  
  .unit-text {
    font-size: 24rpx;
  }
  
  .quick-unit {
    font-size: 22rpx;
    padding: 12rpx 16rpx;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .container {
    padding: 15rpx;
  }
  
  .header {
    margin-bottom: 30rpx;
  }
  
  .category-title {
    font-size: 32rpx;
  }
  
  .category-desc {
    font-size: 20rpx;
  }
  
  .input-section, .result-section {
    margin-bottom: 25rpx;
  }
  
  .convert-arrow {
    margin: 15rpx 0;
  }
  
  .arrow-icon {
    font-size: 32rpx;
  }
  
  .arrow-text {
    font-size: 20rpx;
  }
  
  .quick-units {
    margin-bottom: 25rpx;
  }
  
  .quick-title {
    font-size: 24rpx;
    margin-bottom: 15rpx;
  }
  
  .history-section {
    margin-bottom: 25rpx;
  }
  
  .picker-container {
    max-height: 60vh;
  }
}

/* 高分辨率屏幕适配 */
@media (min-resolution: 2dppx) {
  .card-arrow, .unit-arrow {
    transform: scale(0.8);
  }
  
  .arrow-icon {
    transform: rotate(90deg) scale(0.9);
  }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
  .unit-selector, .quick-unit, .history-item {
    min-height: 88rpx;
  }
  
  .picker-item {
    min-height: 100rpx;
  }
  
  .clear-btn, .copy-btn {
    min-height: 60rpx;
    min-width: 60rpx;
  }
}

/* 换算按钮区域 */
.convert-button-section {
  margin: 60rpx 0;
  text-align: center;
}

.convert-btn {
  width: 400rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  border: none;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.convert-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.2);
}

.convert-btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 结果模式按钮区域 */
.result-button-section {
  margin: 60rpx 0;
  text-align: center;
}

.back-btn {
  width: 400rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border: none;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
}

.back-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(40, 167, 69, 0.2);
}

.back-btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}