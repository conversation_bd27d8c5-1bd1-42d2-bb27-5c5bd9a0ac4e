.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 40rpx 30rpx;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  display: block;
  font-size: 56rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 20rpx;
  letter-spacing: 2rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666666;
  font-weight: 300;
}

.category-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 80rpx;
}

.category-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
}

.category-card:active {
  transform: scale(0.95);
  background: rgba(254, 44, 85, 0.1);
  border-color: rgba(254, 44, 85, 0.3);
}

.card-icon {
  font-size: 60rpx;
  margin-bottom: 20rpx;
  display: block;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 10rpx;
}

.card-desc {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.card-arrow {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #FE2C55;
  font-weight: bold;
}

.footer {
  text-align: center;
  margin-top: auto;
}

.version {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.copyright {
  display: block;
  font-size: 22rpx;
  color: #444444;
}

/* 响应式适配 */
@media (max-width: 768rpx) {
  .container {
    padding: 30rpx 20rpx;
  }
  
  .category-grid {
    gap: 25rpx;
  }
  
  .title {
    font-size: 48rpx;
  }
  
  .subtitle {
    font-size: 26rpx;
  }
}

@media (max-width: 480rpx) {
  .container {
    padding: 20rpx 15rpx;
  }
  
  .header {
    margin-bottom: 60rpx;
  }
  
  .category-grid {
    gap: 20rpx;
    grid-template-columns: 1fr;
  }
  
  .category-card {
    padding: 35rpx 25rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    text-align: left;
  }
  
  .card-icon {
    font-size: 50rpx;
    margin-right: 25rpx;
    margin-bottom: 0;
    flex-shrink: 0;
  }
  
  .card-content {
    flex: 1;
  }
  
  .card-title {
    font-size: 28rpx;
    margin-bottom: 8rpx;
  }
  
  .card-desc {
    font-size: 22rpx;
  }
  
  .card-arrow {
    position: static;
    margin-left: 15rpx;
    font-size: 20rpx;
  }
}

@media (max-width: 375px) {
  .title {
    font-size: 44rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
  }
  
  .category-card {
    padding: 30rpx 20rpx;
  }
  
  .card-icon {
    font-size: 45rpx;
    margin-right: 20rpx;
  }
  
  .card-title {
    font-size: 26rpx;
  }
  
  .card-desc {
    font-size: 20rpx;
  }
}

@media (max-width: 320px) {
  .container {
    padding: 15rpx 10rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
  
  .category-card {
    padding: 25rpx 15rpx;
  }
  
  .card-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
  }
  
  .card-title {
    font-size: 24rpx;
  }
  
  .card-desc {
    font-size: 18rpx;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .header {
    margin-bottom: 40rpx;
  }
  
  .title {
    font-size: 40rpx;
  }
  
  .subtitle {
    font-size: 22rpx;
  }
  
  .category-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15rpx;
  }
  
  .category-card {
    padding: 20rpx 15rpx;
  }
  
  .card-icon {
    font-size: 35rpx;
    margin-bottom: 10rpx;
  }
  
  .card-title {
    font-size: 24rpx;
  }
  
  .card-desc {
    font-size: 18rpx;
  }
  
  .footer {
    margin-top: 30rpx;
  }
}
