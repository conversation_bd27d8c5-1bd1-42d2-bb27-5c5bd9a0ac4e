/**
 * 全局样式
 */

/* 重置样式 */
page {
  background-color: #000000;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.4;
}

/* 全局变量 */
:root {
  --primary-color: #FE2C55;
  --primary-light: rgba(254, 44, 85, 0.1);
  --primary-border: rgba(254, 44, 85, 0.2);
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-card: #ffffff;
  --border-color: rgba(0, 0, 0, 0.08);
  --text-primary: #1a1a1a;
  --text-secondary: #666666;
  --text-disabled: #999999;
  --shadow-light: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
  --radius-small: 12rpx;
  --radius-medium: 16rpx;
  --radius-large: 24rpx;
  --radius-round: 50rpx;
}

/* 通用类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

/* 按钮通用样式 */
.btn {
  border: none;
  border-radius: var(--radius-round);
  font-weight: bold;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:active::after {
  width: 200%;
  height: 200%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, #FF6B9D 100%);
  color: var(--text-primary);
  box-shadow: 0 8rpx 24rpx rgba(254, 44, 85, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-secondary);
  border: 1rpx solid #333333;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
}

.btn-small {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}

.btn-medium {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-large {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
}

/* 卡片样式 */
.card {
  background: var(--bg-card);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius-large);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
}

.card:hover {
  background: #ffffff;
  border-color: rgba(0, 0, 0, 0.12);
  box-shadow: var(--shadow-medium);
}

.card-shadow {
  box-shadow: var(--shadow-light);
}

.card-shadow-medium {
  box-shadow: var(--shadow-medium);
}

/* 输入框样式 */
.input {
  background: rgba(255, 255, 255, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(254, 44, 85, 0.1);
}

.input::placeholder {
  color: var(--text-disabled);
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.scale-in {
  animation: scaleIn 0.2s ease-out;
}

.bounce {
  animation: bounce 0.6s ease-in-out;
}

/* 动画定义 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式断点 */
@media (max-width: 375px) {
  :root {
    --radius-small: 8rpx;
    --radius-medium: 12rpx;
    --radius-large: 16rpx;
  }
  
  .btn-small {
    padding: 6rpx 12rpx;
    font-size: 22rpx;
  }
  
  .btn-medium {
    padding: 12rpx 24rpx;
    font-size: 26rpx;
  }
  
  .btn-large {
    padding: 18rpx 36rpx;
    font-size: 28rpx;
  }
}

@media (max-width: 320px) {
  page {
    font-size: 28rpx;
  }
  
  .btn-small {
    font-size: 20rpx;
  }
  
  .btn-medium {
    font-size: 24rpx;
  }
  
  .btn-large {
    font-size: 26rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-card: rgba(255, 255, 255, 0.08);
    --border-color: rgba(255, 255, 255, 0.15);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --border-color: rgba(255, 255, 255, 0.3);
    --text-secondary: #cccccc;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}

/* 工具类 */
.hidden {
  display: none !important;
}

.invisible {
  visibility: hidden !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.pointer-events-none {
  pointer-events: none !important;
}

.pointer-events-auto {
  pointer-events: auto !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8rpx;
  height: 8rpx;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}