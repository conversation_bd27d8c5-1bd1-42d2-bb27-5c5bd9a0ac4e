const app = getApp();

Page({
  data: {
    type: '', // 换算类型
    adLoaded: false, // 广告是否加载
    loading: false, // 加载状态
    error: false, // 错误状态
    countdown: 15, // 倒计时
    canSkip: false, // 是否可以跳过
    adCompleted: false // 广告是否完成
  },

  onLoad(options) {
    console.log('广告页面加载', options);
    this.setData({
      type: options.type || 'length'
    });
  },

  onShow() {
    console.log('广告页面显示');
  },

  onUnload() {
    // 清理定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },

  // 加载广告
  loadAd() {
    this.setData({
      loading: true,
      error: false
    });

    // 模拟广告加载
    setTimeout(() => {
      if (app.globalData.rewardedVideoAd) {
        // 使用真实的抖音广告SDK
        this.loadRealAd();
      } else {
        // 模拟广告播放
        this.simulateAd();
      }
    }, 1000);
  },

  // 加载真实广告
  loadRealAd() {
    const rewardedVideoAd = app.globalData.rewardedVideoAd;
    
    rewardedVideoAd.load().then(() => {
      console.log('广告加载成功');
      return rewardedVideoAd.show();
    }).then(() => {
      console.log('广告显示成功');
      this.setData({
        loading: false,
        adLoaded: true
      });
      this.startCountdown();
    }).catch((err) => {
      console.error('广告加载失败', err);
      this.setData({
        loading: false,
        error: true
      });
    });
  },

  // 模拟广告播放
  simulateAd() {
    this.setData({
      loading: false,
      adLoaded: true
    });
    this.startCountdown();
  },

  // 开始倒计时
  startCountdown() {
    this.countdownTimer = setInterval(() => {
      const countdown = this.data.countdown - 1;
      this.setData({
        countdown: countdown
      });

      if (countdown <= 0) {
        clearInterval(this.countdownTimer);
        this.setData({
          canSkip: true,
          adCompleted: true
        });
      } else if (countdown <= 5) {
        this.setData({
          canSkip: true
        });
      }
    }, 1000);
  },

  // 跳过广告
  skipAd() {
    if (this.data.adCompleted) {
      // 广告完成，设置观看状态并跳转
      app.globalData.adWatched = true;
      this.navigateToConvert();
    } else {
      // 提前跳过，显示提示
      tt.showModal({
        title: '提示',
        content: '观看完整广告可解锁换算功能，确定要跳过吗？',
        confirmText: '继续观看',
        cancelText: '跳过',
        success: (res) => {
          if (res.cancel) {
            // 跳过广告，但不解锁功能
            tt.navigateBack();
          }
        }
      });
    }
  },

  // 重新加载
  retryLoad() {
    this.setData({
      error: false,
      countdown: 15,
      canSkip: false,
      adCompleted: false
    });
    this.loadAd();
  },

  // 跳过到换算页面（错误情况下）
  skipToConvert() {
    tt.showModal({
      title: '提示',
      content: '广告加载失败，是否直接进入换算功能？',
      success: (res) => {
        if (res.confirm) {
          app.globalData.adWatched = true;
          this.navigateToConvert();
        }
      }
    });
  },

  // 跳转到换算页面
  navigateToConvert() {
    tt.redirectTo({
      url: `/pages/convert/convert?type=${this.data.type}`,
      success: () => {
        console.log('跳转到换算页面成功');
      },
      fail: (err) => {
        console.error('跳转失败', err);
        tt.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  }
});