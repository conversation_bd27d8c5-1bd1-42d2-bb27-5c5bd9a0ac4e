App({
  onLaunch(options) {
    console.log('小程序启动', options);
    
    // 初始化广告SDK
    this.initAd();
    
    // 检查更新
    this.checkUpdate();
  },

  onShow(options) {
    console.log('小程序显示', options);
  },

  onHide() {
    console.log('小程序隐藏');
  },

  onError(error) {
    console.error('小程序错误', error);
  },

  // 初始化广告
  initAd() {
    if (tt.createRewardedVideoAd) {
      this.globalData.rewardedVideoAd = tt.createRewardedVideoAd({
        adUnitId: '2k7xdor211532tu92k' // 替换为实际的广告位ID
      });
      
      this.globalData.rewardedVideoAd.onLoad(() => {
        console.log('激励视频广告加载成功');
      });
      
      this.globalData.rewardedVideoAd.onError((err) => {
        console.error('激励视频广告加载失败', err);
      });
      
      this.globalData.rewardedVideoAd.onClose((res) => {
        if (res && res.isEnded) {
          console.log('用户完整观看了广告');
          this.globalData.adWatched = true;
        } else {
          console.log('用户中途退出广告');
          this.globalData.adWatched = false;
        }
      });
    }
  },

  // 检查更新
  checkUpdate() {
    if (tt.getUpdateManager) {
      const updateManager = tt.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        if (res.hasUpdate) {
          console.log('发现新版本');
        }
      });
      
      updateManager.onUpdateReady(() => {
        tt.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
    }
  },

  globalData: {
    adWatched: false, // 广告观看状态
    rewardedVideoAd: null, // 激励视频广告实例
    userInfo: null,
    // 单位换算数据
    unitData: {
      length: {
        name: '长度',
        icon: '📏',
        units: {
          'm': { name: '米', ratio: 1 },
          'cm': { name: '厘米', ratio: 0.01 },
          'mm': { name: '毫米', ratio: 0.001 },
          'km': { name: '千米', ratio: 1000 },
          'in': { name: '英寸', ratio: 0.0254 },
          'ft': { name: '英尺', ratio: 0.3048 },
          'yd': { name: '码', ratio: 0.9144 }
        }
      },
      weight: {
        name: '重量',
        icon: '⚖️',
        units: {
          'kg': { name: '千克', ratio: 1 },
          'g': { name: '克', ratio: 0.001 },
          't': { name: '吨', ratio: 1000 },
          'lb': { name: '磅', ratio: 0.453592 },
          'oz': { name: '盎司', ratio: 0.0283495 }
        }
      },
      volume: {
        name: '体积',
        icon: '🥤',
        units: {
          'l': { name: '升', ratio: 1 },
          'ml': { name: '毫升', ratio: 0.001 },
          'm3': { name: '立方米', ratio: 1000 },
          'gal': { name: '加仑', ratio: 3.78541 },
          'qt': { name: '夸脱', ratio: 0.946353 }
        }
      },
      temperature: {
        name: '温度',
        icon: '🌡️',
        units: {
          'c': { name: '摄氏度', convert: 'special' },
          'f': { name: '华氏度', convert: 'special' },
          'k': { name: '开尔文', convert: 'special' }
        }
      }
    }
  }
});