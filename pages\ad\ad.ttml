<view class="container">
  <!-- 广告提示区域 -->
  <view class="ad-tip" wx:if="{{!adLoaded}}">
    <view class="tip-icon">📺</view>
    <text class="tip-title">观看广告解锁换算功能</text>
    <text class="tip-desc">观看完整广告后即可使用单位换算功能</text>
    <button class="load-ad-btn" bindtap="loadAd" disabled="{{loading}}">
      {{loading ? '加载中...' : '开始观看'}}
    </button>
  </view>

  <!-- 广告播放区域 -->
  <view class="ad-container" wx:if="{{adLoaded}}">
    <view class="ad-header">
      <text class="ad-title">广告播放中</text>
      <view class="countdown" wx:if="{{countdown > 0}}">
        <text class="countdown-text">{{countdown}}s</text>
      </view>
    </view>
    
    <!-- 模拟广告内容 -->
    <view class="ad-content">
      <view class="ad-video">
        <text class="ad-placeholder">广告内容播放区域</text>
        <text class="ad-brand">品牌广告</text>
      </view>
    </view>

    <!-- 跳过按钮 -->
    <button 
      class="skip-btn" 
      wx:if="{{canSkip}}"
      bindtap="skipAd"
    >
      {{adCompleted ? '开始换算' : '跳过广告'}}
    </button>
  </view>

  <!-- 加载失败提示 -->
  <view class="error-tip" wx:if="{{error}}">
    <view class="error-icon">❌</view>
    <text class="error-title">广告加载失败</text>
    <text class="error-desc">请检查网络连接后重试</text>
    <button class="retry-btn" bindtap="retryLoad">重新加载</button>
    <button class="skip-error-btn" bindtap="skipToConvert">跳过广告</button>
  </view>
</view>