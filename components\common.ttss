/**
 * 通用组件样式
 */

/* 模态框组件 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.modal-container {
  background: var(--bg-secondary);
  border-radius: var(--radius-large);
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;
  animation: scaleIn 0.3s ease-out;
  box-shadow: var(--shadow-heavy);
}

.modal-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-primary);
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--text-secondary);
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.modal-close:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  padding: 20rpx 30rpx 40rpx;
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

/* 底部弹出框 */
.bottom-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.bottom-sheet-container {
  width: 100%;
  max-height: 70vh;
  background: var(--bg-secondary);
  border-radius: var(--radius-large) var(--radius-large) 0 0;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  box-shadow: var(--shadow-heavy);
}

/* 列表组件 */
.list-container {
  background: var(--bg-card);
  border-radius: var(--radius-large);
  overflow: hidden;
}

.list-item {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background: rgba(255, 255, 255, 0.05);
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.list-item-title {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: bold;
}

.list-item-subtitle {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.list-item-extra {
  color: var(--text-secondary);
  font-size: 24rpx;
}

.list-item-arrow {
  color: var(--text-secondary);
  font-size: 24rpx;
  margin-left: 20rpx;
}

.list-item-selected {
  background: var(--primary-light);
  border-color: var(--primary-border);
}

.list-item-selected .list-item-title {
  color: var(--primary-color);
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: var(--radius-round);
  font-size: 24rpx;
  font-weight: bold;
  border: 1rpx solid transparent;
  transition: all 0.3s ease;
}

.tag-primary {
  background: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-border);
}

.tag-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border-color: rgba(255, 255, 255, 0.2);
}

.tag-success {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
}

.tag-warning {
  background: rgba(251, 191, 36, 0.1);
  color: #fbbf24;
  border-color: rgba(251, 191, 36, 0.3);
}

.tag-error {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.tag-small {
  padding: 4rpx 12rpx;
  font-size: 20rpx;
}

.tag-large {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
}

/* 徽章组件 */
.badge {
  position: relative;
  display: inline-block;
}

.badge-dot {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 16rpx;
  height: 16rpx;
  background: var(--primary-color);
  border-radius: 50%;
  border: 2rpx solid var(--bg-primary);
}

.badge-count {
  position: absolute;
  top: -12rpx;
  right: -12rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: var(--primary-color);
  color: white;
  border-radius: var(--radius-round);
  font-size: 20rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  border: 2rpx solid var(--bg-primary);
}

/* 分割线 */
.divider {
  height: 1rpx;
  background: var(--border-color);
  margin: 30rpx 0;
}

.divider-vertical {
  width: 1rpx;
  background: var(--border-color);
  margin: 0 20rpx;
}

.divider-text {
  position: relative;
  text-align: center;
  margin: 40rpx 0;
}

.divider-text::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: var(--border-color);
}

.divider-text text {
  background: var(--bg-primary);
  padding: 0 20rpx;
  color: var(--text-secondary);
  font-size: 24rpx;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  display: block;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  margin-bottom: 15rpx;
  color: var(--text-primary);
}

.empty-description {
  font-size: 26rpx;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

.loading-text {
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-small);
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 32rpx;
  margin-bottom: 16rpx;
}

.skeleton-text:last-child {
  margin-bottom: 0;
  width: 60%;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.skeleton-button {
  height: 64rpx;
  border-radius: var(--radius-round);
}

/* 工具提示 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12rpx 16rpx;
  border-radius: var(--radius-small);
  font-size: 24rpx;
  white-space: nowrap;
  margin-bottom: 8rpx;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1001;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 8rpx solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: 30rpx;
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768rpx) {
  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480rpx) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  
  .grid {
    gap: 20rpx;
  }
}