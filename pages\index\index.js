const app = getApp();

Page({
  data: {
    categories: []
  },

  onLoad() {
    console.log('首页加载');
    this.initCategories();
  },

  onShow() {
    console.log('首页显示');
    // 重置广告观看状态
    app.globalData.adWatched = false;
  },

  // 初始化分类数据
  initCategories() {
    const unitData = app.globalData.unitData;
    const categories = [];
    
    for (let key in unitData) {
      const category = unitData[key];
      categories.push({
        key: key,
        name: category.name,
        icon: category.icon,
        desc: this.getCategoryDesc(key)
      });
    }
    
    this.setData({
      categories: categories
    });
  },

  // 获取分类描述
  getCategoryDesc(type) {
    const descriptions = {
      length: '米、厘米、英寸等',
      weight: '千克、克、磅等',
      volume: '升、毫升、加仑等',
      temperature: '摄氏度、华氏度等'
    };
    return descriptions[type] || '';
  },

  // 导航到换算页面
  navigateToConvert(e) {
    const type = e.currentTarget.dataset.type;

    // 直接跳转到换算页面
    tt.navigateTo({
      url: `/pages/convert/convert?type=${type}`,
      success: () => {
        console.log('跳转到换算页面');
      },
      fail: (err) => {
        console.error('跳转失败', err);
        tt.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '单位换算工具 - 精确换算，简单易用',
      path: '/pages/index/index',
      imageUrl: '/images/share.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '单位换算工具 - 精确换算，简单易用',
      query: '',
      imageUrl: '/images/share.png'
    };
  }
});