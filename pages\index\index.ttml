<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <text class="title">单位换算</text>
    <text class="subtitle">精确换算，简单易用</text>
  </view>

  <!-- 分类卡片网格 -->
  <view class="category-grid">
    <view 
      class="category-card" 
      wx:for="{{categories}}" 
      wx:key="{{item.key}}"
      bindtap="navigateToConvert"
      data-type="{{item.key}}"
    >
      <view class="card-icon">{{item.icon}}</view>
      <view class="card-content">
        <text class="card-title">{{item.name}}</text>
        <text class="card-desc">{{item.desc}}</text>
      </view>
      <view class="card-arrow">→</view>
    </view>
  </view>

</view>