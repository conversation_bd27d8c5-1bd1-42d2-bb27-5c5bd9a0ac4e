.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

/* 广告提示区域 */
.ad-tip {
  text-align: center;
  max-width: 600rpx;
}

.tip-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  display: block;
}

.tip-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 20rpx;
}

.tip-desc {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 60rpx;
}

.load-ad-btn {
  background: linear-gradient(135deg, #FE2C55 0%, #FF6B9D 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(254, 44, 85, 0.3);
}

.load-ad-btn:disabled {
  background: #dee2e6;
  color: #999999;
  box-shadow: none;
}

/* 广告播放区域 */
.ad-container {
  width: 100%;
  max-width: 700rpx;
}

.ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.ad-title {
  font-size: 32rpx;
  color: #1a1a1a;
  font-weight: bold;
}

.countdown {
  background: rgba(254, 44, 85, 0.1);
  border: 1rpx solid #FE2C55;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
}

.countdown-text {
  font-size: 24rpx;
  color: #FE2C55;
  font-weight: bold;
}

.ad-content {
  background: #ffffff;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.ad-video {
  aspect-ratio: 16/9;
  background: linear-gradient(45deg, #e9ecef 0%, #f8f9fa 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.ad-placeholder {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.ad-brand {
  font-size: 24rpx;
  color: #FE2C55;
  font-weight: bold;
}

.skip-btn {
  background: linear-gradient(135deg, #FE2C55 0%, #FF6B9D 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  width: 100%;
  box-shadow: 0 8rpx 24rpx rgba(254, 44, 85, 0.3);
}

/* 错误提示区域 */
.error-tip {
  text-align: center;
  max-width: 600rpx;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  display: block;
}

.error-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #dc3545;
  margin-bottom: 20rpx;
}

.error-desc {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 60rpx;
}

.retry-btn {
  background: linear-gradient(135deg, #FE2C55 0%, #FF6B9D 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  width: 100%;
}

.skip-error-btn {
  background: transparent;
  color: #666666;
  border: 1rpx solid rgba(0, 0, 0, 0.12);
  border-radius: 50rpx;
  padding: 20rpx 50rpx;
  font-size: 28rpx;
  width: 100%;
}

/* 动画效果 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.countdown {
  animation: pulse 1s infinite;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .container {
    padding: 30rpx;
  }
  
  .tip-icon, .error-icon {
    font-size: 100rpx;
  }
  
  .tip-title, .error-title {
    font-size: 32rpx;
  }
  
  .load-ad-btn, .skip-btn, .retry-btn {
    font-size: 26rpx;
    padding: 18rpx 40rpx;
  }
}