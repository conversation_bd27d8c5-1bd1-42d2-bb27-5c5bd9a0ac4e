{"description": "抖音小程序单位换算工具项目配置文件", "packOptions": {"ignore": [{"type": "file", "value": ".eslintrc.js"}, {"type": "file", "value": ".giti<PERSON>re"}, {"type": "file", "value": "README.md"}, {"type": "folder", "value": "docs"}]}, "setting": {"urlCheck": true, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": true, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "3.0.0", "appid": "tt140f62b28b06a7c201", "projectname": "unit-converter-douyin", "debugOptions": {"hidedInDevtools": []}, "scripts": {}, "staticServerOptions": {"baseURL": "", "servePath": ""}, "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": [{"name": "首页", "pathName": "pages/index/index", "query": "", "scene": null}, {"name": "长度换算", "pathName": "pages/convert/convert", "query": "type=length", "scene": null}, {"name": "重量换算", "pathName": "pages/convert/convert", "query": "type=weight", "scene": null}, {"name": "体积换算", "pathName": "pages/convert/convert", "query": "type=volume", "scene": null}, {"name": "温度换算", "pathName": "pages/convert/convert", "query": "type=temperature", "scene": null}, {"name": "广告页面", "pathName": "pages/ad/ad", "query": "type=length", "scene": null}]}}}