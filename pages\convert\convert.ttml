<view class="container">
  <!-- 顶部标题区域 -->
  <view class="header">
    <text class="category-title">{{resultMode ? '换算结果' : categoryInfo.name + '换算'}}</text>
    <text class="category-desc">{{resultMode ? '广告播放完成，以下是您的换算结果' : categoryInfo.desc}}</text>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <view class="input-card">
      <view class="input-header">
        <text class="input-label">输入数值</text>
        <button class="clear-btn" bindtap="clearInput" wx:if="{{inputValue}}">清除</button>
      </view>
      
      <view class="input-group">
        <input 
          class="number-input" 
          type="digit"
          placeholder="请输入数值"
          value="{{inputValue}}"
          bindinput="onInputChange"
          focus="{{inputFocus}}"
        />
        <view class="unit-selector" bindtap="showFromUnitPicker">
          <text class="unit-text">{{fromUnit.name}}</text>
          <text class="unit-arrow">▼</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 转换箭头 -->
  <view class="convert-arrow" bindtap="swapUnits">
    <view class="arrow-icon">⇅</view>
    <text class="arrow-text">点击交换</text>
  </view>

  <!-- 结果区域 -->
  <view class="result-section">
    <view class="result-card">
      <view class="result-header">
        <text class="result-label">换算结果</text>
        <button class="copy-btn" bindtap="copyResult" wx:if="{{resultValue}}">复制</button>
      </view>

      <view class="result-group">
        <text class="result-value">{{resultMode ? resultValue : (inputValue ? '****' : '0')}}</text>
        <view class="unit-selector" bindtap="showToUnitPicker">
          <text class="unit-text">{{toUnit.name}}</text>
          <text class="unit-arrow">▼</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 换算按钮 -->
  <view class="convert-button-section" wx:if="{{inputValue && resultValue && !resultMode}}">
    <button class="convert-btn" bindtap="performConvert">
      <text class="convert-btn-text">换算</text>
    </button>
  </view>

  <!-- 结果模式按钮 -->
  <view class="result-button-section" wx:if="{{resultMode}}">
    <button class="back-btn" bindtap="backToConvert">
      <text class="back-btn-text">重新换算</text>
    </button>
  </view>

  <!-- 常用单位快捷选择 -->
  <view class="quick-units">
    <text class="quick-title">常用单位</text>
    <scroll-view class="quick-scroll" scroll-x="true">
      <view class="quick-unit-list">
        <view 
          class="quick-unit {{item.key === fromUnit.key ? 'active' : ''}}" 
          wx:for="{{commonUnits}}" 
          wx:key="{{item.key}}"
          bindtap="selectQuickUnit"
          data-unit="{{item.key}}"
          data-type="from"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 历史记录 -->
  <view class="history-section" wx:if="{{historyList.length > 0}}">
    <view class="history-header">
      <text class="history-title">历史记录</text>
      <button class="clear-history-btn" bindtap="clearHistory">清空</button>
    </view>
    <view class="history-list">
      <view 
        class="history-item" 
        wx:for="{{historyList}}" 
        wx:key="{{index}}"
        bindtap="useHistoryItem"
        data-item="{{item}}"
      >
        <text class="history-text">{{item.input}} {{item.fromUnit}} = {{item.result}} {{item.toUnit}}</text>
        <text class="history-time">{{item.time}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 单位选择器弹窗 -->
<view class="picker-mask" wx:if="{{showPicker}}" bindtap="hidePicker">
  <view class="picker-container" catchtap="">
    <view class="picker-header">
      <text class="picker-title">选择{{pickerType === 'from' ? '输入' : '输出'}}单位</text>
      <button class="picker-close" bindtap="hidePicker">×</button>
    </view>
    <scroll-view class="picker-list" scroll-y="true">
      <view 
        class="picker-item {{item.key === (pickerType === 'from' ? fromUnit.key : toUnit.key) ? 'selected' : ''}}" 
        wx:for="{{unitList}}" 
        wx:key="{{item.key}}"
        bindtap="selectUnit"
        data-unit="{{item.key}}"
      >
        <text class="picker-unit-name">{{item.name}}</text>
        <text class="picker-unit-key">{{item.key}}</text>
        <view class="picker-check" wx:if="{{item.key === (pickerType === 'from' ? fromUnit.key : toUnit.key)}}">✓</view>
      </view>
    </scroll-view>
  </view>
</view>